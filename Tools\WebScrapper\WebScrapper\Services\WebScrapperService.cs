using WebScrapper.Helpers;
using WebScrapper.Models;

namespace WebScrapper.Services;

public class WebScrapperService: IWebScrapperService
{
    private readonly IHeadLessBrowserHelper _headLessBrowserHelper;
    private readonly ILogger<WebScrapperService> _logger;

    public WebScrapperService(IHeadLessBrowserHelper headLessBrowserHelper, ILogger<WebScrapperService> logger)
    {
        _headLessBrowserHelper = headLessBrowserHelper;
        _logger = logger;
    }

    public async Task<List<PersonInfo>> GetPersonDetailsAsync(string url)
    {
        return await _headLessBrowserHelper.GetNameListFromChRegisterAsync(url);
    }
}
