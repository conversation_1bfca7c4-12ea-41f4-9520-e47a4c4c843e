# WebScrapper API architectural plan

## Defining the API's Purpose and Scope:

### Business Use Cases: 
Currently in ALAN, when we use efix API, we get information about a company we search for. But the contact person and their details are not included in this API. They are shown in the cantonal websites which we scrape.
But the problem we faced is that the cantonal websites change their structures often and that's why we need to catch up to their changes frequently. Without modifying our source code of web scraping, customers do not get the contact persons details when the cantonal websites are changed.
To mitigate this issue, we proposed to separate the Web Scraping part from the ALAN application itself and deploy it in separate environment so that, whenever we fix the scraping logic, we can deploy it separately of ALAN. This prevents the need for frequent hotfix release of ALAN. 

### Target Audience:
The API will only be used by ALAN application, nothing else will use it.

### Functional Requirements:
The API should return the contact person details of a company.

## Non-Functional Requirements
- **Performance**: The API should respond within 2 seconds for 95% of requests under normal load.
- **Scalability**: The API should handle multiple concurrent requests efficiently.
- **Availability**: Target uptime will depend on the uptime of the hosting provider. Deploy with redundancy and failover in mind. The user count is relatively very small, so there is not much chance of unavailability due to high load.
- **Maintainability**: Scraping logic should be modular and easily updatable without downtime for the API.

## Choosing an Architectural Style:
REST is the most suitable choice for our use case due to its simplicity and use of standard HTTP methods.

## Defining Data Formats and Protocols:
- **Data Serialization**: JSON for data exchange. 
- **Protocol**: HTTP for standard communication between clients and servers.
- **Encoding**: UTF-8

## Security Considerations:
- **Authentication**: Implement mechanisms to verify user identities (JWT).
- **Authorization**: Not needed, we only have 1 API in this project. If someone can call the API, they can see the data.
- **Encryption**: Protect data in transit and at rest using encryption protocols (e.g., SSL/TLS). 

## API Endpoint Design
- **Endpoint Structure**:
  - `GET /api/PersonDetails?cantonCode=<canton>&uid=<company-uid>`
- **Request Example**:
  - `GET /api/PersonDetails?cantonCode=zh&uid=CHE-442.398.437`
- **Parameters**:
  - `cantonCode`: 2-letter lowercase canton code (e.g., zh, be, gl, bs)
  - `uid`: Company UID in format CHE-XXX.XXX.XXX
- **Response Example**:
```json
[
  {
    "PersonalDetails": "Udo Schneider",
    "Role": "manager",
    "SigningAuthority": "individual signing authority"
  },
  {
    "PersonalDetails": "Zehnder, Michael, von Schlatt (ZH), in Dinhard",
    "Role": "member of the board",
    "SigningAuthority": "individual signing authority"
  }
]
```

## Error Handling
- **Error Response Structure**: All error responses will use HTTP status codes (e.g., 400 for bad request, 404 for not found, 500 for internal error) and return a JSON body:
```json
{
  "error": "Company not found",
  "code": 404
}
```
- **Common Error Scenarios**:
  - Company not found
  - Scraping failure (website structure changed)
  - Invalid input
  - Internal server error (scraping logic error)
  - Authentication failure
  - Rate limiting exceeded
  - Request timeout
  - Cantonal website is not available

## Logging and Monitoring
- **Logging**: Log all requests, responses, and errors for traceability and debugging. Serilog is used for logging. The logs are persisted on the host VPS and rotated daily. We can use cloud based logging in future if needed.
- **Monitoring**: Integrate with monitoring tools to track API health, uptime, and performance metrics. Will be added later, not a high priority for us right away.
- **Tools**: We can use either mod_status and .NET health checks, which is the most basic form of monitoring without much historical data and dashboard facility. Or we can use Prometheus + Grafana which is the best free and easy-to-integrate choice.
But the downside is that we need to have 2 services running (grafana & prometheus)

## Deployment & Environment
- **Deployment Strategy**: Deploy as a containerized service (e.g., Docker) in a cloud or on-premise environment. Use CI/CD for automated deployments.
- **Environment Configuration**: Manage environment-specific settings (e.g., scraping targets, secrets) via environment variables or a secure configuration service.

<!-- ## Testing
- **Testing Strategy**: Implement unit, and end-to-end tests for all API endpoints and scraping logic.
- **Mocking External Dependencies**: Use mocks or stubs for cantonal websites to test scraping logic safely and reliably. -->

## Rate Limiting & Abuse Prevention
- **Rate Limiting**: A user can request upto 15 request per minutes. Rate limiting will be applied from Apache, not from DotNet Kestrel Server.

## API Documentation and Design:

### API Specification: 
Swagger/OpenAPI will be used to create a detailed API specification.

### Documentation: 
Provide clear and comprehensive documentation for developers, including usage instructions, examples, and error codes. 

### Versioning:
A versioning strategy to be defined later to manage API evolution and compatibility. For now, it is not needed as such, since there is no need for multiple versions of this API to exist.
