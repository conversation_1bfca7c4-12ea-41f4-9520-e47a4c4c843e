﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WebScrapper.Models;
using WebScrapper.Services;
using WebScrapper.Validators;
using System.Text.RegularExpressions;

namespace WebScrapper.Controllers;
[Authorize]
[ApiController]
[Route("api/[controller]")]
public class PersonDetailsController: ControllerBase
{
    private readonly IWebScrapperService _webScrapperService;
    private readonly ILogger<PersonDetailsController> _logger;
    private readonly ChRegisterUrlValidator _urlValidator;

    public PersonDetailsController(
        IWebScrapperService webScrapperService, 
        ILogger<PersonDetailsController> logger,
        ChRegisterUrlValidator urlValidator)
    {
        _webScrapperService = webScrapperService;
        _logger = logger;
        _urlValidator = urlValidator;
    }

    [HttpGet(Name = "GetPersonDetails")]
    public async Task<ActionResult<IEnumerable<PersonInfo>>> GetPeople(
        [FromQuery] string cantonCode,
        [FromQuery] string uid)
    {
        // Validate input parameters
        if (string.IsNullOrWhiteSpace(cantonCode) || string.IsNullOrWhiteSpace(uid))
        {
            _logger.LogWarning("Missing required parameters. CantonCode: {CantonCode}, UID: {Uid}", cantonCode, uid);
            return BadRequest("Both cantonCode and uid parameters are required. Example: ?cantonCode=zh&uid=CHE-442.398.437");
        }

        // Validate canton code format (2 lowercase letters)
        if (cantonCode.Length != 2 || !cantonCode.All(char.IsLower) || !cantonCode.All(char.IsLetter))
        {
            _logger.LogWarning("Invalid canton code format: {CantonCode}", cantonCode);
            return BadRequest("Canton code must be exactly 2 lowercase letters (e.g., 'zh', 'be', 'gl')");
        }

        // Validate UID format (CHE-XXX.XXX.XXX)
        if (!uid.StartsWith("CHE-") || uid.Length != 15 || !Regex.IsMatch(uid, @"^CHE-\d{3}\.\d{3}\.\d{3}$"))
        {
            _logger.LogWarning("Invalid UID format: {Uid}", uid);
            return BadRequest("UID must be in format CHE-XXX.XXX.XXX (e.g., 'CHE-442.398.437')");
        }

        // Construct the full URL
        var url = $"https://{cantonCode}.chregister.ch/cr-portal/auszug/auszug.xhtml?uid={uid}";

        _logger.LogInformation("Processing request for canton: {CantonCode}, UID: {Uid}", cantonCode, uid);

        var people = await _webScrapperService.GetPersonDetailsAsync(url);

        if(people == null || people.Count == 0)
        {
            _logger.LogError("No people found for canton: {CantonCode}, UID: {Uid}", cantonCode, uid);
            return NotFound("No people found");
        }
        return Ok(people);
    }
}
