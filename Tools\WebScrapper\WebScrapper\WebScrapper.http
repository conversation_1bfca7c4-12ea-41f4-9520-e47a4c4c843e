@WebScrapper_HostAddress = http://localhost:5106

GET {{WebScrapper_HostAddress}}/api/Test/
Accept: application/json

###

GET {{WebScrapper_HostAddress}}/api/PersonDetails?cantonCode=zh&uid=CHE-442.398.437
Authorization: Basic YWxhbmFkbWluOmFsYW4hQDIxQUxBTg==
Accept: application/json

###

# Test with different canton
GET {{WebScrapper_HostAddress}}/api/PersonDetails?cantonCode=be&uid=CHE-238.529.600
Authorization: Basic YWxhbmFkbWluOmFsYW4hQDIxQUxBTg==
Accept: application/json

###

# Test with invalid canton code
GET {{WebScrapper_HostAddress}}/api/PersonDetails?cantonCode=ZH&uid=CHE-442.398.437
Authorization: Basic YWxhbmFkbWluOmFsYW4hQDIxQUxBTg==
Accept: application/json

###

# Test with invalid UID format
GET {{WebScrapper_HostAddress}}/api/PersonDetails?cantonCode=zh&uid=CHE-442398437
Authorization: Basic YWxhbmFkbWluOmFsYW4hQDIxQUxBTg==
Accept: application/json

###
