services:
  webscrapper:
    image: ${DOCKER_REGISTRY-}webscrapper
    build:
      context: ./WebScrapper
      dockerfile: Dockerfile
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - Authentication__BasicAuth__Username=${BASIC_AUTH_USERNAME}
      - Authentication__BasicAuth__Password=${BASIC_AUTH_PASSWORD}
    ports:
      - "8080:8080"
    restart: unless-stopped
    volumes:
      - ./logs:/app/Logs
        
