using HtmlAgilityPack;
using Microsoft.Playwright;
using WebScrapper.Models;
using System.Diagnostics;

namespace WebScrapper.Helpers;

public class HeadLessBrowserHelper: IHeadLessBrowserHelper
{
    private readonly ILogger<HeadLessBrowserHelper> _logger;
    private readonly string basePath = AppDomain.CurrentDomain.BaseDirectory;

    public HeadLessBrowserHelper(ILogger<HeadLessBrowserHelper> logger)
    {
        _logger = logger;
    }

    public async Task<List<PersonInfo>> GetNameListFromChRegisterAsync(string uri)
    {
        _logger.LogInformation("called for the url: " + uri);
        var stopwatch = Stopwatch.StartNew();
        if(string.IsNullOrWhiteSpace(uri))
        {
            return Enumerable.Empty<PersonInfo>().ToList();
        }

        try
        {
            using var playwright = await Playwright.CreateAsync();
            await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
            {
                Headless = true,
                Args = new[] {
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-gpu",
                    "--lang=de-CH"
                }
            });

            var context = await browser.NewContextAsync(new BrowserNewContextOptions
            {
                Locale = "de-CH"
            });

            var page = await context.NewPageAsync();

            var germanUri = uri;
            if (uri.Contains(".chregister.ch") && !uri.Contains("lang=") && !uri.Contains("locale="))
            {
                var separator = uri.Contains("?") ? "&" : "?";
                germanUri = $"{uri}{separator}lang=de";
            }

            await page.GotoAsync(germanUri, new PageGotoOptions { Timeout = 20000, WaitUntil = WaitUntilState.NetworkIdle });

            // Wait for the table to appear (up to 7s)
            try
            {
                await page.WaitForSelectorAsync("table.personen,table.person", new PageWaitForSelectorOptions { Timeout = 7000 });
            }
            catch (TimeoutException)
            {
                _logger.LogWarning("Timeout waiting for person table on {Uri}", germanUri);
            }

            var htmlContent = await page.ContentAsync();

            if (string.IsNullOrWhiteSpace(htmlContent))
            {
                _logger.LogError("Empty page source received from {Uri}", uri);
                return Enumerable.Empty<PersonInfo>().ToList();
            }

            _logger.LogInformation("Page source found, length: {Length}", htmlContent.Length);

            var detectedLanguage = DetectPageLanguage(htmlContent);
            _logger.LogInformation("Detected page language: {Language}", detectedLanguage);

            try {
                var debugPath = Path.Combine(basePath, "debug_page.html");
                File.WriteAllText(debugPath, htmlContent);
                _logger.LogInformation("Saved debug HTML to {Path}", debugPath);
            } catch (Exception ex) {
                _logger.LogWarning("Could not save debug HTML: {Error}", ex.Message);
            }

            var people = await findPersonDetailsList(htmlContent);

            if (people.Count == 0)
            {
                _logger.LogError("No people found for {Uri}", uri);
            }
            else
            {
                _logger.LogInformation("Found {Count} people for {Uri}", people.Count, uri);
            }

            stopwatch.Stop();
            _logger.LogInformation("Scraping completed in {ElapsedMs} ms", stopwatch.ElapsedMilliseconds);
            return people;
        }
        catch(Exception ex)
        {
            _logger.LogError("Error getting person data: {Error}", ex.ToString());
            return Enumerable.Empty<PersonInfo>().ToList();
        }
    }

    private string DetectPageLanguage(string htmlContent)
    {
        try
        {
            var doc = new HtmlDocument();
            doc.LoadHtml(htmlContent);
            var htmlNode = doc.DocumentNode.SelectSingleNode("//html");
            if (htmlNode?.GetAttributeValue("lang", "") is string langAttr && !string.IsNullOrEmpty(langAttr))
            {
                return $"HTML lang='{langAttr}'";
            }
            var bodyText = doc.DocumentNode.SelectSingleNode("//body")?.InnerText?.ToLower() ?? "";
            var germanKeywords = new[] { "handelsregister", "firma", "geschäftsführer", "verwaltungsrat", "einzelfirma", "gesellschaft" };
            var englishKeywords = new[] { "commercial register", "company", "manager", "board", "individual", "corporation" };
            var germanCount = germanKeywords.Count(keyword => bodyText.Contains(keyword));
            var englishCount = englishKeywords.Count(keyword => bodyText.Contains(keyword));
            if (germanCount > englishCount)
                return $"German (keywords: {germanCount} German vs {englishCount} English)";
            else if (englishCount > germanCount)
                return $"English (keywords: {englishCount} English vs {germanCount} German)";
            else
                return "Unknown (no clear language indicators)";
        }
        catch (Exception ex)
        {
            return $"Detection failed: {ex.Message}";
        }
    }

    private async Task<List<PersonInfo>> findPersonDetailsList(string htmlContent)
    {
        var doc = new HtmlDocument();
        doc.LoadHtml(htmlContent);
        List<PersonInfo> people = new();
        var table = doc.DocumentNode.SelectSingleNode("//table[contains(@class, 'personen')]");
        if(table == null)
        {
            _logger.LogWarning("No table with class 'personen' found. Trying alternative selectors...");
            table = doc.DocumentNode.SelectSingleNode("//table[contains(@class, 'person')]");
            if(table == null)
            {
                _logger.LogWarning("No table with class 'person' found. Trying to find any table...");
                var allTables = doc.DocumentNode.SelectNodes("//table");
                if(allTables != null && allTables.Count > 0)
                {
                    _logger.LogInformation("Found {Count} tables on the page", allTables.Count);
                    foreach(var t in allTables)
                    {
                        var headers = t.SelectNodes(".//th");
                        if(headers != null)
                        {
                            foreach(var header in headers)
                            {
                                var headerText = header.InnerText.ToLower();
                                if(headerText.Contains("name") || headerText.Contains("person") || 
                                   headerText.Contains("rolle") || headerText.Contains("funktion"))
                                {
                                    _logger.LogInformation("Found potential person table with header: {Header}", headerText);
                                    table = t;
                                    break;
                                }
                            }
                        }
                        if(table != null) break;
                    }
                }
                else
                {
                    _logger.LogWarning("No tables found on the page");
                    return Enumerable.Empty<PersonInfo>().ToList();
                }
            }
        }
        if(table == null)
        {
            _logger.LogWarning("Could not find any suitable table for person data");
            return Enumerable.Empty<PersonInfo>().ToList();
        }
        var rows = table.SelectNodes(".//tbody/tr");
        if(rows == null)
        {
            _logger.LogWarning("No rows found in tbody. Trying direct tr selector...");
            rows = table.SelectNodes(".//tr");
        }
        if(rows == null)
        {
            _logger.LogWarning("No rows found in the table");
            return Enumerable.Empty<PersonInfo>().ToList();
        }
        _logger.LogInformation("Found {Count} rows in the table", rows.Count);
        foreach(var row in rows)
        {
            var tds = row.SelectNodes("td");
            if(tds != null)
            {
                _logger.LogInformation("Processing row with {Count} cells", tds.Count);
                if(tds.Count >= 6)
                {
                    var nameTd = tds[3];
                    var classVal = nameTd.GetAttributeValue("class", "");
                    if(!classVal.Contains("strike"))
                    {
                        var person = new PersonInfo
                        {
                            PersonalDetails = nameTd.InnerText.Trim(),
                            Role = tds[4].InnerText.Trim(),
                            SigningAuthority = tds[5].InnerText.Trim()
                        };
                        _logger.LogInformation("Found person: {Details}, {Role}", person.PersonalDetails, person.Role);
                        people.Add(person);
                    }
                }
                else if(tds.Count >= 3)
                {
                    var nameTd = tds[0];
                    var person = new PersonInfo
                    {
                        PersonalDetails = nameTd.InnerText.Trim(),
                        Role = tds.Count > 1 ? tds[1].InnerText.Trim() : "",
                        SigningAuthority = tds.Count > 2 ? tds[2].InnerText.Trim() : ""
                    };
                    _logger.LogInformation("Found person (alt format): {Details}, {Role}", person.PersonalDetails, person.Role);
                    people.Add(person);
                }
            }
        }
        return await Task.FromResult(people);
    }
}
