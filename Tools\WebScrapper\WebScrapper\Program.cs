﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http.Timeouts;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.OpenApi.Models;
using Serilog;
using WebScrapper.Handler;
using WebScrapper.Helpers;
using WebScrapper.Middleware;
using WebScrapper.Services;

var builder = WebApplication.CreateBuilder(args);

// Configure Kestrel Server Options (FIRST - affects how server handles connections)
builder.Services.Configure<KestrelServerOptions>(options =>
{
    options.Limits.MaxConcurrentConnections = 25;        // Slightly higher than Apache limit
    options.Limits.MaxRequestBodySize = 5 * 1024 * 1024; // 5MB
    options.Limits.RequestHeadersTimeout = TimeSpan.FromSeconds(15);
    options.Limits.KeepAliveTimeout = TimeSpan.FromSeconds(60);
    options.Limits.MaxRequestHeaderCount = 50;           // Match Apache LimitRequestFields
    options.Limits.MaxRequestHeadersTotalSize = 8192;    // 8KB total headers
});

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .CreateLogger();

builder.Host.UseSerilog(); // Use Serilog instead of default logging

// Add services to the container.
builder.Services.AddControllers();

// Add Request Timeouts (BEFORE authentication - affects all requests)
builder.Services.AddRequestTimeouts(options =>
{
    options.DefaultPolicy = new RequestTimeoutPolicy
    {
        Timeout = TimeSpan.FromSeconds(2*60) // 2 minutes to match my web scraping time
    };
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSingleton<IWebScrapperService, WebScrapperService>();
builder.Services.AddSingleton<IHeadLessBrowserHelper, HeadLessBrowserHelper>();

// Add Basic Authentication (AFTER basic services, request timeouts)
builder.Services.AddAuthentication("BasicAuthentication")
    .AddScheme<AuthenticationSchemeOptions, BasicAuthenticationHandler>("BasicAuthentication", null);

builder.Services.AddSwaggerGen(c =>
{
    c.AddSecurityDefinition("basic", new OpenApiSecurityScheme
    {
        Name = "Authorization",
        Type = SecuritySchemeType.Http,
        Scheme = "basic",
        In = ParameterLocation.Header,
        Description = "Basic Authentication"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "basic" }
            },
            Array.Empty<string>()
        }
    });
});

var app = builder.Build();
// Enable Request Timeouts Middleware (EARLY in pipeline, before auth) so that even authenticated requests are timeouted
app.UseRequestTimeouts();

// Register error handling middleware first to catch all exceptions
app.UseMiddleware<ErrorHandlingMiddleware>();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();
